# BPM 流程表达式使用指南

## 📋 什么是流程表达式

流程表达式（Process Expression）是BPM工作流中用于动态计算和条件判断的脚本语言，基于**UEL（Unified Expression Language）**语法。它可以在流程运行时访问流程变量、调用Spring Bean方法，实现复杂的业务逻辑。

## 🎯 主要用途

### 1. **网关条件判断**
在排他网关（Exclusive Gateway）中设置分支条件：
```
${day > 3}  // 请假天数大于3天走经理审批
${amount > 10000}  // 金额大于1万元走财务审批
```

### 2. **任务分配**
动态计算任务的候选人：
```
${bpmTaskAssignStartUserExpression.calculateUsers(execution)}
```

### 3. **监听器调用**
在监听器中执行业务逻辑：
```
${notificationService.sendEmail(assignee, taskName)}
```

### 4. **条件路由**
在简化流程设计器中设置条件分支：
```
${priority == "urgent" || emergency == true}
```

## 🔧 表达式语法

### 基本语法
- **变量访问**：`${variableName}`
- **属性访问**：`${user.name}`
- **方法调用**：`${service.method(param)}`
- **运算符**：`+`, `-`, `*`, `/`, `%`, `==`, `!=`, `>`, `<`, `>=`, `<=`
- **逻辑运算**：`&&`, `||`, `!`
- **条件运算**：`condition ? value1 : value2`

### 数据类型
- **字符串**：`"hello"` 或 `'hello'`
- **数字**：`123`, `3.14`
- **布尔值**：`true`, `false`
- **null值**：`null`

## 📊 系统中的表达式示例

### 1. **请假天数大于3天**
```
${day > 3}
```
**用途**：请假流程中，超过3天需要经理审批

### 2. **金额大于1万元**
```
${amount > 10000}
```
**用途**：报销流程中，超过1万元需要财务审批

### 3. **发起人是管理员**
```
${startUserId == 1}
```
**用途**：特殊权限判断，管理员可以走快速通道

### 4. **工作日申请**
```
${dayOfWeek >= 1 && dayOfWeek <= 5}
```
**用途**：只允许工作日提交申请

### 5. **紧急申请**
```
${priority == "urgent" || emergency == true}
```
**用途**：紧急申请走特殊流程

### 6. **调用Spring Bean方法**
```
${bpmTaskAssignStartUserExpression.calculateUsers(execution)}
```
**用途**：动态计算任务分配人

## 🚀 如何使用流程表达式

### 1. **创建表达式模板**

#### 通过管理界面：
1. 访问：**系统管理 → BPM管理 → 流程表达式**
2. 点击"新增"创建表达式
3. 填写表达式名称和表达式内容

#### 通过API：
```bash
curl -X POST http://localhost:48080/admin-api/bpm/process-expression/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "自定义条件",
    "status": 0,
    "expression": "${customVariable > 100}"
  }'
```

### 2. **在流程设计中使用**

#### 排他网关条件：
1. 在BPMN设计器中选择网关的出线
2. 在属性面板中设置"条件表达式"
3. 输入表达式：`${day > 3}`

#### 简化流程设计器：
1. 添加条件节点
2. 选择"条件表达式"类型
3. 输入或选择已创建的表达式

#### 任务分配：
1. 选择用户任务节点
2. 在"分配规则"中选择"表达式"
3. 输入表达式：`${userService.getManager(startUserId)}`

## 🔍 可用的内置变量

### 流程变量
- **`startUserId`**：流程发起人ID
- **`processInstanceId`**：流程实例ID
- **`processDefinitionId`**：流程定义ID
- **`businessKey`**：业务键
- **`tenantId`**：租户ID

### 任务变量（在任务上下文中）
- **`taskId`**：任务ID
- **`assignee`**：任务分配人
- **`candidateUsers`**：候选用户列表
- **`candidateGroups`**：候选组列表

### 执行上下文
- **`execution`**：当前执行对象
- **`task`**：当前任务对象（任务监听器中）

## 🎯 高级用法

### 1. **调用Spring Bean**
```
${@userService.getUserById(startUserId).deptId}
${@deptService.getDeptLeader(deptId)}
${@notificationService.sendMessage(assignee, "新任务待处理")}
```

### 2. **复杂条件判断**
```
${(amount > 10000 && type == "expense") || (day > 7 && type == "leave")}
```

### 3. **字符串操作**
```
${userName.length() > 0 && userName.startsWith("admin")}
${reason.contains("紧急") ? "urgent" : "normal"}
```

### 4. **日期时间处理**
```
${T(java.time.LocalDate).now().getDayOfWeek().getValue() <= 5}
${startTime.isBefore(T(java.time.LocalDateTime).now().plusDays(7))}
```

### 5. **集合操作**
```
${candidateUsers.contains(currentUserId)}
${approvers.size() > 2}
```

## ⚠️ 注意事项

### 1. **性能考虑**
- 避免在表达式中执行耗时操作
- 复杂计算建议在Service层完成，表达式只做简单判断

### 2. **异常处理**
- 表达式执行异常会导致流程中断
- 使用条件运算符进行空值检查：`${user != null ? user.name : "unknown"}`

### 3. **安全性**
- 不要在表达式中暴露敏感信息
- 限制表达式的执行权限

### 4. **调试技巧**
- 使用简单表达式进行测试
- 在开发环境中启用详细日志
- 通过流程变量查看中间结果

## 📖 实际应用场景

### 场景1：请假审批流程
```
// 网关条件
${day <= 3}  → 直接审批通过
${day > 3 && day <= 7}  → 部门经理审批
${day > 7}  → 部门经理 + HR审批
```

### 场景2：采购申请流程
```
// 网关条件
${amount <= 1000}  → 部门经理审批
${amount > 1000 && amount <= 10000}  → 部门经理 + 财务审批
${amount > 10000}  → 部门经理 + 财务 + 总经理审批
```

### 场景3：动态任务分配
```
// 任务分配表达式
${@userService.getDeptManager(startUser.deptId)}  // 分配给部门经理
${@roleService.getUsersByRole("FINANCE_MANAGER")}  // 分配给财务经理角色
```

流程表达式是BPM系统中非常强大的功能，可以让你的工作流更加智能和灵活！🎉
