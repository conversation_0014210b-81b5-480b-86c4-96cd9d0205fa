# 项目启动指南

## 📋 项目概述

本项目基于芋道源码的 yudao-vue-pro 框架，采用前后端分离架构：
- **后端**：Spring Boot 2.7 + MyBatis Plus + Redis
- **前端**：Vue 3 + Element Plus + TypeScript + Vite
- **数据库**：支持 MySQL、PostgreSQL、Oracle 等多种数据库

## 🛠️ 环境要求

### 后端环境
- **JDK**: 8 或以上版本
- **Maven**: 3.6+ 
- **数据库**: MySQL 5.7+ / PostgreSQL 12+ / Oracle 11g+
- **Redis**: 5.0+

### 前端环境
- **Node.js**: 16.18.0+ (推荐使用 18.x)
- **pnpm**: 8.6.0+ (强制使用 pnpm，不支持 npm/yarn)

## 📦 快速启动

### 1. 数据库准备

#### 1.1 创建数据库
根据你使用的数据库类型，创建对应的数据库：

**MySQL:**
```sql
CREATE DATABASE workflow_yudao DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**PostgreSQL:**
```sql
CREATE DATABASE workflow_yudao;
```

#### 1.2 导入数据库脚本
根据数据库类型，导入对应的SQL文件：

**MySQL:**
```bash
# 导入主数据库脚本
mysql -u root -p workflow_yudao < yudao-server/sql/mysql/ruoyi-vue-pro.sql

# 导入定时任务脚本（可选）
mysql -u root -p workflow_yudao < yudao-server/sql/mysql/quartz.sql
```

**PostgreSQL:**
```bash
# 导入主数据库脚本
psql -U postgres -d workflow_yudao -f yudao-server/sql/postgresql/ruoyi-vue-pro.sql

# 导入定时任务脚本（可选）
psql -U postgres -d workflow_yudao -f yudao-server/sql/postgresql/quartz.sql
```

### 2. Redis 准备

确保 Redis 服务已启动：
```bash
# 启动 Redis（根据你的安装方式）
redis-server

# 或者使用 Docker
docker run -d --name redis -p 6379:6379 redis:latest
```

### 3. 后端启动

#### 3.1 修改配置文件
编辑 `yudao-server/yudao-server/src/main/resources/application-local.yaml`：

```yaml
spring:
  datasource:
    druid:
      primary: master
      datasource:
        master:
          # MySQL 配置示例
          url: ****************************************************************************************************************************************************************************
          username: root
          password: 123456
          
          # PostgreSQL 配置示例（注释掉 MySQL，启用此配置）
          # url: ***********************************************
          # username: postgres
          # password: 123456

  # Redis 配置
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0
    # password: # 如果有密码请取消注释并填写
```

#### 3.2 启动后端服务
```bash
# 进入后端目录
cd yudao-server

# 使用 Maven 启动（推荐）
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 或者使用 IDE 直接运行 YudaoServerApplication.java
```

启动成功后，后端服务将运行在 `http://localhost:48080`

### 4. 前端启动

#### 4.1 安装依赖
```bash
# 进入前端目录
cd yudao-ui-admin-vue3

# 安装依赖（必须使用 pnpm）
pnpm install
```

#### 4.2 启动前端服务
```bash
# 启动开发服务器
pnpm dev

# 或者连接开发环境后端
pnpm dev-server
```

启动成功后，前端服务将运行在 `http://localhost:80`

## 🔐 默认账号

启动成功后，使用以下账号登录系统：

- **超级管理员**
  - 账号：`admin`
  - 密码：`admin123`

- **普通管理员**  
  - 账号：`test`
  - 密码：`test123`

## 🚀 访问地址

- **前端管理后台**: http://localhost:80
- **后端接口文档**: http://localhost:48080/doc.html
- **后端监控中心**: http://localhost:48080/admin

## 📝 开发说明

### 项目结构
```
├── docs/                    # 文档目录
├── yudao-server/           # 后端项目
│   ├── yudao-dependencies/ # 依赖管理
│   ├── yudao-framework/    # 框架封装
│   ├── yudao-module-*/     # 业务模块
│   ├── yudao-server/       # 启动模块
│   └── sql/               # 数据库脚本
└── yudao-ui-admin-vue3/   # 前端项目
    ├── src/               # 源码目录
    ├── public/            # 静态资源
    └── package.json       # 依赖配置
```

### 开发模式
- **本地开发**: 使用 `local` 配置文件，连接本地数据库和Redis
- **开发环境**: 使用 `dev` 配置文件，连接开发环境服务
- **生产环境**: 使用 `prod` 配置文件，连接生产环境服务

### 常用命令

**后端:**
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package -Dmaven.test.skip=true
```

**前端:**
```bash
# 类型检查
pnpm ts:check

# 代码格式化
pnpm lint:format

# 构建生产版本
pnpm build:prod
```

## ❗ 常见问题

### 1. 后端启动失败
- 检查数据库连接配置是否正确
- 确认数据库脚本是否已正确导入
- 检查 Redis 服务是否正常运行
- 查看控制台错误日志定位具体问题

### 2. 前端启动失败
- 确认 Node.js 版本是否符合要求（16.18.0+）
- 必须使用 pnpm 安装依赖，不支持 npm/yarn
- 删除 `node_modules` 重新安装依赖

### 3. 登录失败
- 检查后端服务是否正常启动
- 确认数据库中是否有用户数据
- 检查 Redis 连接是否正常

### 4. 接口调用失败
- 检查前端代理配置是否正确
- 确认后端接口地址是否可访问
- 查看浏览器网络面板和控制台错误信息

## 📚 相关文档

- **官方文档**: https://doc.iocoder.cn
- **视频教程**: https://doc.iocoder.cn/video/
- **接口文档**: https://doc.iocoder.cn/api-doc/
- **快速开始**: https://doc.iocoder.cn/quick-start/

## 🤝 技术支持

如遇到问题，可通过以下方式获取帮助：
- 查看官方文档和FAQ
- 在项目 Issues 中搜索相关问题
- 加入官方技术交流群

---

**注意**: 首次启动可能需要较长时间下载依赖，请耐心等待。建议使用国内镜像源加速下载。
