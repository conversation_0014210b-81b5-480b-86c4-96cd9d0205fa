package com.ruyi.jx.dynamic.node.controller;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.ruyi.jx.dynamic.node.DynamicNodeService;
import com.ruyi.jx.dynamic.node.vo.DynamicNodeRequestVO;
import com.ruyi.jx.dynamic.node.vo.DynamicNodeResponseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 动态子节点控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 动态子节点")
@RestController
@RequestMapping("/admin-api/bpm/dynamic-node")
@Validated
@Slf4j
public class DynamicNodeController {

    @Resource
    private DynamicNodeService dynamicNodeService;

    @PostMapping("/get-child-nodes")
    @Operation(summary = "获取动态子节点列表")
    @PreAuthorize("@ss.hasPermission('bpm:process-definition:query')")
    public CommonResult<List<DynamicNodeResponseVO>> getDynamicChildNodes(
            @Valid @RequestBody DynamicNodeRequestVO request) {
        log.info("[getDynamicChildNodes] 接收到获取动态子节点请求: {}", request);
        return dynamicNodeService.getDynamicChildNodes(request);
    }

    @GetMapping("/validate-config")
    @Operation(summary = "验证动态节点配置")
    @PreAuthorize("@ss.hasPermission('bpm:process-definition:update')")
    public CommonResult<Boolean> validateDynamicNodeConfig(
            @Parameter(description = "节点ID", required = true) @RequestParam("nodeId") String nodeId,
            @Parameter(description = "HTTP接口地址", required = true) @RequestParam("httpUrl") String httpUrl) {
        log.info("[validateDynamicNodeConfig] 验证动态节点配置，节点ID: {}, HTTP地址: {}", nodeId, httpUrl);
        return dynamicNodeService.validateDynamicNodeConfig(nodeId, httpUrl);
    }

    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新动态节点缓存")
    @PreAuthorize("@ss.hasPermission('bpm:process-definition:update')")
    public CommonResult<Boolean> refreshDynamicNodeCache(
            @Parameter(description = "节点ID", required = true) @RequestParam("nodeId") String nodeId) {
        log.info("[refreshDynamicNodeCache] 刷新动态节点缓存，节点ID: {}", nodeId);
        return dynamicNodeService.refreshDynamicNodeCache(nodeId);
    }

    @GetMapping("/test-connection")
    @Operation(summary = "测试HTTP连接")
    @PreAuthorize("@ss.hasPermission('bpm:process-definition:query')")
    public CommonResult<String> testConnection(
            @Parameter(description = "HTTP接口地址", required = true) @RequestParam("httpUrl") String httpUrl) {
        log.info("[testConnection] 测试HTTP连接: {}", httpUrl);
        
        // 创建测试请求
        DynamicNodeRequestVO testRequest = new DynamicNodeRequestVO();
        testRequest.setParentNodeId("test_node");
        testRequest.setHttpUrl(httpUrl);
        testRequest.setUseCache(false);
        
        CommonResult<List<DynamicNodeResponseVO>> result = dynamicNodeService.getDynamicChildNodes(testRequest);
        
        if (result.isSuccess()) {
            return CommonResult.success("连接测试成功，返回 " + result.getData().size() + " 个节点");
        } else {
            return CommonResult.error(result.getCode(), "连接测试失败: " + result.getMsg());
        }
    }
}
