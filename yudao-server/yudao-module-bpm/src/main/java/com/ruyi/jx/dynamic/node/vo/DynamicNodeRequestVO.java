package com.ruyi.jx.dynamic.node.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 动态子节点请求VO
 * 
 * <AUTHOR>
 */
@Schema(description = "动态子节点请求VO")
@Data
public class DynamicNodeRequestVO {

    @Schema(description = "父节点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "Activity_123")
    @NotEmpty(message = "父节点ID不能为空")
    private String parentNodeId;

    @Schema(description = "流程实例ID", example = "12345")
    private String processInstanceId;

    @Schema(description = "流程定义ID", example = "process_def_123")
    private String processDefinitionId;

    @Schema(description = "HTTP接口地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "http://api.example.com/nodes")
    @NotEmpty(message = "HTTP接口地址不能为空")
    private String httpUrl;

    @Schema(description = "HTTP请求方法", example = "GET")
    private String httpMethod = "GET";

    @Schema(description = "HTTP请求头", example = "{\"Content-Type\": \"application/json\"}")
    private Map<String, String> httpHeaders;

    @Schema(description = "HTTP请求参数", example = "{\"parentId\": \"123\", \"type\": \"child\"}")
    private Map<String, Object> httpParams;

    @Schema(description = "流程变量", example = "{\"userId\": \"1001\", \"deptId\": \"2001\"}")
    private Map<String, Object> processVariables;

    @Schema(description = "是否使用缓存", example = "true")
    private Boolean useCache = true;

    @Schema(description = "缓存过期时间（秒）", example = "300")
    private Integer cacheExpireTime = 300;
}
