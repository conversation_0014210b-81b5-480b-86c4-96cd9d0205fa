package com.ruyi.jx.dynamic.node.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 动态子节点响应VO
 * 
 * <AUTHOR>
 */
@Schema(description = "动态子节点响应VO")
@Data
public class DynamicNodeResponseVO {

    @Schema(description = "子节点ID", example = "Activity_child_001")
    private String nodeId;

    @Schema(description = "子节点名称", example = "部门审批")
    private String nodeName;

    @Schema(description = "子节点类型", example = "11")
    private Integer nodeType;

    @Schema(description = "子节点显示文本", example = "指定成员: 张三")
    private String showText;

    @Schema(description = "子节点图标", example = "user")
    private String nodeIcon;

    @Schema(description = "子节点颜色", example = "#1890ff")
    private String nodeColor;

    @Schema(description = "子节点配置", example = "{\"candidateUsers\": [\"user1\", \"user2\"]}")
    private Map<String, Object> nodeConfig;

    @Schema(description = "子节点属性", example = "{\"priority\": 1, \"timeout\": 3600}")
    private Map<String, Object> nodeProperties;

    @Schema(description = "子节点条件表达式", example = "${amount > 1000}")
    private String conditionExpression;

    @Schema(description = "子节点排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled = true;

    @Schema(description = "子节点描述", example = "当金额大于1000时需要部门经理审批")
    private String description;

    @Schema(description = "扩展属性", example = "{\"customField1\": \"value1\"}")
    private Map<String, Object> extendProperties;
}
