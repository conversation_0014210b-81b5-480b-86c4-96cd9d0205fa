package com.ruyi.jx.dynamic.node.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.ruyi.jx.dynamic.node.DynamicNodeService;
import com.ruyi.jx.dynamic.node.vo.DynamicNodeRequestVO;
import com.ruyi.jx.dynamic.node.vo.DynamicNodeResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 动态子节点服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class DynamicNodeServiceImpl implements DynamicNodeService {

    @Value("${dynamic-node.http-timeout:5000}")
    private Integer httpTimeout;

    @Value("${dynamic-node.enabled:true}")
    private Boolean dynamicNodeEnabled;

    @Override
    @Cacheable(value = "dynamic_node", key = "#request.parentNodeId + '_' + #request.httpUrl.hashCode()", 
               condition = "#request.useCache == true")
    public CommonResult<List<DynamicNodeResponseVO>> getDynamicChildNodes(DynamicNodeRequestVO request) {
        log.info("[getDynamicChildNodes] 开始获取动态子节点，父节点ID: {}, HTTP地址: {}", 
                request.getParentNodeId(), request.getHttpUrl());

        // 检查功能是否启用
        if (!dynamicNodeEnabled) {
            log.warn("[getDynamicChildNodes] 动态子节点功能未启用");
            return error(500, "动态子节点功能未启用");
        }

        try {
            // 构建HTTP请求
            HttpRequest httpRequest = buildHttpRequest(request);
            
            // 发送HTTP请求
            HttpResponse response = httpRequest.timeout(httpTimeout).execute();
            
            if (!response.isOk()) {
                log.error("[getDynamicChildNodes] HTTP请求失败，状态码: {}, 响应: {}", 
                         response.getStatus(), response.body());
                return error(500, "HTTP请求失败: " + response.getStatus());
            }

            // 解析响应数据
            String responseBody = response.body();
            log.debug("[getDynamicChildNodes] HTTP响应: {}", responseBody);

            List<DynamicNodeResponseVO> childNodes = parseHttpResponse(responseBody);
            
            log.info("[getDynamicChildNodes] 成功获取动态子节点，数量: {}", childNodes.size());
            return success(childNodes);

        } catch (Exception e) {
            log.error("[getDynamicChildNodes] 获取动态子节点异常", e);
            return error(500, "获取动态子节点失败: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<Boolean> validateDynamicNodeConfig(String nodeId, String httpUrl) {
        log.info("[validateDynamicNodeConfig] 验证动态节点配置，节点ID: {}, HTTP地址: {}", nodeId, httpUrl);

        if (StrUtil.isBlank(nodeId)) {
            return error(400, "节点ID不能为空");
        }

        if (StrUtil.isBlank(httpUrl)) {
            return error(400, "HTTP接口地址不能为空");
        }

        try {
            // 发送测试请求
            HttpResponse response = HttpRequest.get(httpUrl)
                    .timeout(httpTimeout)
                    .execute();

            if (response.isOk()) {
                log.info("[validateDynamicNodeConfig] 动态节点配置验证成功");
                return success(true);
            } else {
                log.warn("[validateDynamicNodeConfig] 动态节点配置验证失败，状态码: {}", response.getStatus());
                return error(500, "HTTP接口不可访问，状态码: " + response.getStatus());
            }

        } catch (Exception e) {
            log.error("[validateDynamicNodeConfig] 验证动态节点配置异常", e);
            return error(500, "验证失败: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = "dynamic_node", key = "#nodeId + '_*'")
    public CommonResult<Boolean> refreshDynamicNodeCache(String nodeId) {
        log.info("[refreshDynamicNodeCache] 刷新动态节点缓存，节点ID: {}", nodeId);
        return success(true);
    }

    /**
     * 构建HTTP请求
     */
    private HttpRequest buildHttpRequest(DynamicNodeRequestVO request) {
        HttpRequest httpRequest;
        
        // 根据请求方法创建请求
        if ("POST".equalsIgnoreCase(request.getHttpMethod())) {
            httpRequest = HttpRequest.post(request.getHttpUrl());
            // 设置请求体
            if (CollUtil.isNotEmpty(request.getHttpParams())) {
                httpRequest.body(JSONUtil.toJsonStr(request.getHttpParams()));
            }
        } else {
            httpRequest = HttpRequest.get(request.getHttpUrl());
            // 设置查询参数
            if (CollUtil.isNotEmpty(request.getHttpParams())) {
                for (Map.Entry<String, Object> entry : request.getHttpParams().entrySet()) {
                    httpRequest.form(entry.getKey(), entry.getValue());
                }
            }
        }

        // 设置请求头
        if (CollUtil.isNotEmpty(request.getHttpHeaders())) {
            for (Map.Entry<String, String> entry : request.getHttpHeaders().entrySet()) {
                httpRequest.header(entry.getKey(), entry.getValue());
            }
        }

        // 设置默认请求头
        httpRequest.header("Content-Type", "application/json");
        httpRequest.header("User-Agent", "Ruyi-Workflow-DynamicNode/1.0");

        return httpRequest;
    }

    /**
     * 解析HTTP响应数据
     */
    private List<DynamicNodeResponseVO> parseHttpResponse(String responseBody) {
        List<DynamicNodeResponseVO> result = new ArrayList<>();

        try {
            // 尝试解析为JSON数组
            if (JSONUtil.isJsonArray(responseBody)) {
                List<Map<String, Object>> dataList = JSONUtil.toList(responseBody, Map.class);
                for (Map<String, Object> data : dataList) {
                    DynamicNodeResponseVO node = convertToNodeResponse(data);
                    if (node != null) {
                        result.add(node);
                    }
                }
            } else if (JSONUtil.isJsonObj(responseBody)) {
                // 尝试解析为包含data字段的JSON对象
                Map<String, Object> responseMap = JSONUtil.toBean(responseBody, Map.class);
                Object dataObj = responseMap.get("data");
                
                if (dataObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> dataList = (List<Map<String, Object>>) dataObj;
                    for (Map<String, Object> data : dataList) {
                        DynamicNodeResponseVO node = convertToNodeResponse(data);
                        if (node != null) {
                            result.add(node);
                        }
                    }
                } else if (dataObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) dataObj;
                    DynamicNodeResponseVO node = convertToNodeResponse(data);
                    if (node != null) {
                        result.add(node);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[parseHttpResponse] 解析HTTP响应异常", e);
        }

        return result;
    }

    /**
     * 转换数据为节点响应对象
     */
    private DynamicNodeResponseVO convertToNodeResponse(Map<String, Object> data) {
        if (CollUtil.isEmpty(data)) {
            return null;
        }

        DynamicNodeResponseVO node = new DynamicNodeResponseVO();
        
        // 设置基本属性
        node.setNodeId(getStringValue(data, "nodeId", "id"));
        node.setNodeName(getStringValue(data, "nodeName", "name"));
        node.setNodeType(getIntegerValue(data, "nodeType", "type"));
        node.setShowText(getStringValue(data, "showText", "text"));
        node.setNodeIcon(getStringValue(data, "nodeIcon", "icon"));
        node.setNodeColor(getStringValue(data, "nodeColor", "color"));
        node.setConditionExpression(getStringValue(data, "conditionExpression", "condition"));
        node.setSortOrder(getIntegerValue(data, "sortOrder", "order"));
        node.setEnabled(getBooleanValue(data, "enabled"));
        node.setDescription(getStringValue(data, "description", "desc"));

        // 设置配置和属性
        Object configObj = data.get("nodeConfig");
        if (configObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> config = (Map<String, Object>) configObj;
            node.setNodeConfig(config);
        }

        Object propertiesObj = data.get("nodeProperties");
        if (propertiesObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> properties = (Map<String, Object>) propertiesObj;
            node.setNodeProperties(properties);
        }

        Object extendObj = data.get("extendProperties");
        if (extendObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> extend = (Map<String, Object>) extendObj;
            node.setExtendProperties(extend);
        }

        return node;
    }

    /**
     * 获取字符串值（支持多个字段名）
     */
    private String getStringValue(Map<String, Object> data, String... fieldNames) {
        for (String fieldName : fieldNames) {
            Object value = data.get(fieldName);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }

    /**
     * 获取整数值（支持多个字段名）
     */
    private Integer getIntegerValue(Map<String, Object> data, String... fieldNames) {
        for (String fieldName : fieldNames) {
            Object value = data.get(fieldName);
            if (value != null) {
                if (value instanceof Number) {
                    return ((Number) value).intValue();
                } else {
                    try {
                        return Integer.parseInt(value.toString());
                    } catch (NumberFormatException e) {
                        // 忽略转换异常
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取布尔值
     */
    private Boolean getBooleanValue(Map<String, Object> data, String fieldName) {
        Object value = data.get(fieldName);
        if (value != null) {
            if (value instanceof Boolean) {
                return (Boolean) value;
            } else {
                return Boolean.parseBoolean(value.toString());
            }
        }
        return true; // 默认启用
    }
}
