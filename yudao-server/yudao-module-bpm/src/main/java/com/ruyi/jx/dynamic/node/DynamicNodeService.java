package com.ruyi.jx.dynamic.node;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.ruyi.jx.dynamic.node.vo.DynamicNodeRequestVO;
import com.ruyi.jx.dynamic.node.vo.DynamicNodeResponseVO;

import java.util.List;

/**
 * 动态子节点服务接口
 * 
 * <AUTHOR>
 */
public interface DynamicNodeService {

    /**
     * 根据父节点信息动态获取子节点列表
     * 
     * @param request 动态节点请求参数
     * @return 子节点列表
     */
    CommonResult<List<DynamicNodeResponseVO>> getDynamicChildNodes(DynamicNodeRequestVO request);

    /**
     * 验证动态节点配置是否有效
     * 
     * @param nodeId 节点ID
     * @param httpUrl HTTP接口地址
     * @return 验证结果
     */
    CommonResult<Boolean> validateDynamicNodeConfig(String nodeId, String httpUrl);

    /**
     * 刷新动态节点缓存
     * 
     * @param nodeId 节点ID
     * @return 刷新结果
     */
    CommonResult<Boolean> refreshDynamicNodeCache(String nodeId);
}
