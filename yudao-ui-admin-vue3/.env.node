# 动态子节点开发环境：用于动态子节点功能开发和测试
NODE_ENV=development

VITE_DEV=true

# 项目本地运行端口号 - 动态子节点专用端口
VITE_PORT=83

# 请求路径 - 使用动态子节点专用后端端口
VITE_BASE_URL='http://localhost:2583'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://localhost:3000'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'

# open 运行 npm run dev 时自动打开浏览器
VITE_OPEN=true
